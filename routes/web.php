<?php

use App\Http\Controllers\DataMapController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\MapController;
use App\Http\Controllers\NominatimController;
use App\Http\Controllers\PlaceMapController;
use App\Models\Cell;
use App\Models\District;
use App\Models\Sector;
use App\Models\Village;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cache;
use Spinen\Geometry\Geometry;
use App\Models\Province;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Services\OsrmService;

Route::get('', [HomeController::class, 'landing'])->name('landing');

Route::middleware(['auth:sanctum',  config('jetstream.auth_session'), 'verified',])->group(function () {
    Route::get('dashboard', [HomeController::class, 'dashboard'])->name('dashboard');

    Route::post('geo-fancing', [PlaceMapController::class, 'updateGeoFancing'])->name('placeMap.updateGeoFancing');
    Route::post('gps-data', [PlaceMapController::class, 'storeGpsData'])->name('placeMap.storeGpsData');

    Route::prefix('my-maps')->group(function () {
        Route::get('', [PlaceMapController::class, 'myMap'])->name('myMap.index');
        Route::get('{placeMapId}', [PlaceMapController::class, 'myMapItems'])->name('myMap.show')->whereNumber('placeMapId');
    });

    Route::prefix('my-map-data')->group(function () {
        Route::get('{placeMapId}', [DataMapController::class, 'myMapData'])->name('myMapData.index')->whereNumber('placeMapId');
        Route::get('{placeMapId}/{dataMapId}', [DataMapController::class, 'myMapDataItems'])->name('myMapData.show')->whereNumber('placeMapId', 'dataMapId');
    });

    Route::prefix('map')->group(function () {
        Route::prefix('place')->group(function () {
            //place map json
            Route::get('', [PlaceMapController::class, 'index'])->name('placeMap.index');
            Route::post('', [PlaceMapController::class, 'store'])->name('placeMap.store');
            Route::post('{placeMapId}', [PlaceMapController::class, 'update'])->name('placeMap.update')->whereNumber('placeMapId');
            Route::get('{placeMapId}', [PlaceMapController::class, 'getPlaceMapById'])->name('placeMap.getById')->whereNumber('placeMapId');

            //place map items json
            Route::post('{placeMapId}/create-place', [PlaceMapController::class, 'createPlaceItem'])->name('placeMap.createPlaceItem')->whereNumber('placeMapId');
            Route::post('{placeMapId}/{placeMapItemId}/update-place', [PlaceMapController::class, 'updatePlaceItem'])->name('placeMap.updatePlaceItem')->whereNumber('placeMapId', 'placeMapItemId');
            Route::get('{placeMapId}/items', [PlaceMapController::class, 'getPlaceMapItem'])->name('placeMap.getPlaceMapItem')->whereNumber('placeMapId');
            Route::get('{placeMapId}/{placeMapItemId}', [PlaceMapController::class, 'getPlaceMapItemById'])->name('placeMap.getPlaceMapItemById')->whereNumber('placeMapId', 'placeMapItemId');

            // geoFancing json

        });
        Route::prefix('shared')->group(function () {
            Route::get('{mapKey}', [PlaceMapController::class, 'sharedMap'])->name('sharedMap.index');
            Route::get('map-data/{mapKey}', [PlaceMapController::class, 'sharedMapData'])->name('sharedMapData.index');
        });

        Route::prefix('data')->group(function () {

            //data map json
            Route::get('{placeMapId}', [DataMapController::class, 'index'])->name('dataMap.index')->whereNumber('placeMapId');
            Route::post('{placeMapId}', [DataMapController::class, 'store'])->name('dataMap.store')->whereNumber('placeMapId');
            Route::put('{placeMapId}/{dataMapId}', [DataMapController::class, 'update'])->name('dataMap.update')->whereNumber('placeMapId', 'dataMapId');
            Route::get('{placeMapId}/{dataMapId}', [DataMapController::class, 'getDataMapById'])->name('dataMap.getById')->whereNumber('placeMapId', 'dataMapId');

            //data map items json
            Route::get('items/{dataMapId}', [DataMapController::class, 'getDataMapItem'])->name('dataMap.getDataMapItem')->whereNumber('dataMapId');
            Route::post('{dataMapId}/create-data-item', [DataMapController::class, 'createDataItem'])->name('dataMap.createDataItem')->whereNumber('dataMapId');
            Route::put('{dataMapId}/{dataMapItemId}/update-data-item', [DataMapController::class, 'updateDataItem'])->name('dataMap.updateDataItem')->whereNumber('dataMapId', 'dataMapItemId');

            // data map items custom data json
            Route::post('create-custom-data', [DataMapController::class, 'createCustomData'])->name('dataMap.createCustomData');
            Route::delete('remove-custom-data', [DataMapController::class, 'removeCustomData'])->name('dataMap.removeCustomData');
            Route::put('update-custom-data', [DataMapController::class, 'updateCustomData'])->name('dataMap.updateCustomData');
            Route::get('get-custom-data', [DataMapController::class, 'getCustomData'])->name('dataMap.getCustomData');
        });
    });
});

Route::prefix('map')->group(function () {
    Route::get('distance', [MapController::class, 'distance'])->name('distance');
    Route::get('', [MapController::class, 'index'])->name('map');

    Route::get('search', [MapController::class, 'search'])->name('search');
    Route::post('search-json', [MapController::class, 'searchJson'])->name('search.json');

    Route::get('search-latitude-langitude', [MapController::class, 'searchLatitudeLongitude'])->name('search.latitudeLongitude');
    Route::post('search-latitude-langitude-json', [MapController::class, 'searchLatitudeLongitudeJson'])->name('search.latitudeLongitudeJson');
    Route::get('api', [MapController::class, 'mapApiSearch'])->name('mapApi.search');
    Route::prefix('place')->group(function () {
        Route::post('', [PlaceMapController::class, 'store'])->name('placeMap.store');
    });
});

Route::prefix('nominatim')->group(function () {
    Route::get('search', [NominatimController::class, 'search'])->name('nominatim.search');
});



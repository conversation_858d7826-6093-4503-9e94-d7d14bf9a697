1. when user is creating dataMap we will check if the map they are on has custom fiels adn add them ,  our own added custom fiels will not be reomoved or chnaged , or edited , 

2. data item being single or multi will be defined by admin 

3. when user is using gps anfd geofancing at the same time 

4. when user is creating map item and the item is trackable we will create its API, we need its ucrrent location  when the tacking details are emtpty,  api will also be disabled by default , when user enables geo fannce will be able to choose geofance depending on geofance fields , it can also be active or not , 


i you to create gofacning Nitification using our our laravel builtin notification <?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GeoFenceNotification  , on also on my app how to add optio to trigger geofacning , i have option to add geofacing to placeonmap then , ihave option oto receive that moving item on map live gps cordibnated , and       │
│   i have option user can enable  thqat , and every item on geo fance has a gson field whic has plogyone , like chehere @app/Models/District.php , @app/Models/Province.php , @app/Models/Sector.php , also here in my       │
│   servicer i have implementation in maps @app/Services/MapService.php  abnd in datamap @app/Services/DataMapService.php  , here is also my routes @routes/web.php  , here are my controllers @app/Http/Controllers/ ,       │
│   for more descriptio of how i need to imprement it  check the config array for geofancing here @config/geo-data.php on  // for placemapitem                                                                                │
│       'geo-fancing' => [                                                                                                                                                                                                    │
│                                                                                                                                                                                                                             │
│   we have alread have option to enable geofancing on placeMap and even select district or village to goofance on , so what we need is to complete this testing , it('can create notification when geo-fancing is            │
│   triggered', function ()                                                                                                                                                                                                   │
│   this newn test will create place map , place map item data map , dadtamaitem then add villlage or district , then item scross the geofancing , then  create notification in placemapservice thna in test chekc if         │
│   ouser can read that nitification  


